# CPAAS-42054: As an Enterprise Admin or user, I shall be able to simulate the conversation between the end user and the bot using the Preview Panel for a WebBoT

**Status:** To Do  
**Priority:** Minor

---

## Description

Description: A Preview button is available at the top of the bot builder canvas.Clicking the Preview button opens a Preview drawer pane from the right side.By default, the preview simulates the chatbot in WebBot view.Dropdown allows channel selection (but only WebBot view is in scope for this story).Preview panel has three top-right options:Experience in Website → Opens configured webpage with bot.Debug → Opens debug drawer from bottom.Close → Opens confirmation modal; on confirm, preview closes, user returns to canvas.Preview pane allows end-user-like interaction:EA/EU types a message in the input field.Sends message either via Send button or Enter key.Message is displayed in conversational space.<PERSON><PERSON> responds as per configured flows.
Pre-condition: EA/EU logged into NGAGE platform and has the required permission to create/edit the flow.At least one bot flow exists in the bot builder.
Actors: Enterprise Admin (EA)Enterprise User (EU)
Workflow: EA/EU logs into the platform and clicks on a bot created or creates a bot from scratch.Navigates to bot builder and ensures a flow exists.Clicks Preview button at top.Preview drawer opens from right side in WebBot view.EA/EU types a message and sends via button/Enter.Message appears in preview chat space, bot replies accordingly.EA/EU can switch top options:Experience in Website opens the bot webpage view.Debug opens bottom debug drawer.Close prompts confirmation → returns to canvas.
Post-condition: Bot flow is previewed in WebBot simulation.EA/EU validated flow correctness before publishing.Closing preview returns user to the flow canvas unchanged.
Message Validation Table: FieldValidation RuleError MessageMessage InputCannot be blank“Message cannot be empty.”Message InputMax length 500 chars“Message cannot exceed 500 characters.”File/Media InputNot supported in preview (WebBot-only scope for this story)“File/media input not supported in Preview.”Preview CloseConfirmation modal before exit“Are you sure you want to close Preview?”

---

## Comments

_No comments_