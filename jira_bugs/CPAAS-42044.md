# CPAAS-42044: Trigger Node| The selected intent is not visible in the Label dropdown of the trigger node if the user selects the intent and reloads the page.

**Status:** To Do  
**Priority:** High

---

## Description

Description: Trigger Node| The selected intent is not visible in the Label dropdown of the trigger node if the user selects the intent and reloads the page.Pre-requisite:
Intent implementation is doneSteps to reproduce:Log in to CPaaS  with Enterprise admin credentialsNow, navigate to NeuraTalk AI Click on the Create buttonOn the chatbot builder, click the Train tabSelect INTENT UTTERANCES sub-tabClick on the ADD INTENT buttonFill the details and ADD the intent Add some utterances to the intentIn the design tab, add a Trigger nodeClick on the Trigger node and link it with the created intentNow close the window.Now, reload the page.Click on the Trigger node to reopen the left window.Check the added Intent in the Label dropdown.Expected Result:The added intent should be visible in the Label dropdown of the Trigger node.Actual Result:The user can see a blank field if they reopen the left window of the trigger node.Screenshots:-

---

## Comments

_No comments_