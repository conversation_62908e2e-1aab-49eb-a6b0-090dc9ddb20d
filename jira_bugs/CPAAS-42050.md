# CPAAS-42050: NeuraTalk| No error message is displayed when user is trying to link a deleted flow with FAQ

**Status:** To Do  
**Priority:** Medium

---

## Description

Description: No error message is displayed when user is trying to link a deleted flow with FAQPre-requisite:
User is logged in to neuratalk portal with enterprise user or enterprise admin accountAn FAQ is already added in the bot with a link flow attachedSteps to reproduce:Login as Enterprise Admin Click on the to Neuratalk module.select any exisitng bot.Open a separate tab and navigate to same screeclick on the �Train� tabOpen FAQ modal by clicking in the FAQ sub tab. Edit any listed FAQ or create a new oneIn the ADD/EDIT FAQ popup select one flowOn the previous tab, click on the overflow menu of the flow selected in previous step Delete the flowTry to Save the flow in other tabExpected Result:Validate message is displayedActual ResultNothing happens but no validation message is displayed

---

## Comments

_No comments_