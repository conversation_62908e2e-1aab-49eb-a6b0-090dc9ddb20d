# CPAAS-41917: [FAQ]: Delete confirmation popup shows same message while deleting category with/without FAQs

**Status:** To Do  
**Priority:** Medium

---

## Description

Description: As per the confirmation from <PERSON><PERSON>, 
the confirmation message should be different in both cases

Case 1: When there is at least one FAQ added in the category
”Do you want to delete the category? All ‘X' FAQs will be lost.”
Here X denotes the number of FAQs present in the category during deletion. Case 2: When there are no FAQs added in the category
”Do you want to delete the category?”
Pre-requisite:Valid Enterprise Admin/User should existsSteps to Reproduce:Log in to the CPAAS application.Navigate to the 'Neuratalk AI 'pageClick on any existing bot or click on the ‘CREATE’ buttonNavigate to ‘Train' → 'FAQs’ tabCreate a category (e.g., "Two-Step Deletion Test") and add at least one FAQ to it.Click the "Delete" icon/button for this category.Observe the confirmation flow (or lack thereof).Expected Results: The delete confirmation pop-up should have a description  "Do you want to delete the category? All 'X' FAQs will be lost.""Do you want to delete the category?" should only be displayed in case the category with no FAQs is deleted.Actual Results: The delete confirmation pop-up should have a description  "Do you want to delete the category?"Attachments:

---

## Comments

_No comments_