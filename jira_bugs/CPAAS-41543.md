# CPAAS-41543: As an Enterprise Admin or enterprise user, I shall be able to preview the bot’s experience on a website by uploading a background image and launching the web widget over it

**Status:** To Do  
**Priority:** Minor

---

## Description

As an Enterprise Admin or enterprise user, I shall be able to preview the bot’s experience on a website by uploading a background image and launching the web widget over it, so that I can visualize how the chatbot will look and function on a real webpage.

Within the Deploy sub-tab (under Channels → Web/Mobile Bot → Deploy), there is a section called "Experience on a website." This feature allows Enterprise Admins or users to upload a background image (representing a webpage) and preview how the chatbot widget will appear and function over it. The preview reflects the branding guidelines configured for the widget.Supported formats: .jpg, .pngSupported size: Up to 5 MB (suggested limit for webpage background usability and system performance)If an image already exists, uploading a new one replaces the previous one.Once uploaded, the image is displayed in the background when previewing the "Experience on a website."If no image is uploaded, it shows a default ‘Comviva’ branding image at the background.Clicking on the "Preview on Web" option launches a live preview where the chatbot widget is fully functional and interactive.
Actors: Enterprise AdminEnterprise User
Pre-condition: Enterprise Admin or user has access to Channels → Web/Mobile Bot.A chatbot has been created and branding guidelines (colors, icons, etc.) are already set under Widget Settings.The user is on the Deploy sub-tab.
Workflow: Enterprise Admin or user navigates to Channels → Web/Mobile Bot.User selects the Deploy sub-tab.User sees the section "Experience on a website."In the section, user sees an option to upload an image which can be clicked. A dialog box (modal) opens asking the user to upload an image.System shows format restriction (.jpg, .png) and size restriction (≤ 5 MB).If an image already exists, system indicates that uploading a new image will replace the old one.User uploads the image.If successful, system confirms and stores the image.If unsuccessful, system shows appropriate validation/error messages.When the user clicks on "Experience on a website," the system loads a preview page with:The uploaded image as background.The chatbot web widget displayed (based on branding).A fully functional chatbot experience for testing.User clicks on “Preview on Web' button which redirects the user to a different website. User can interact with the chatbot in this preview environment.
Post-condition: The uploaded image is saved and associated with the "Experience on a website" feature.On click of ‘Preview on Web’, user is directed to a different webpage replicating the web-widget, working fully-functional and with the background updated image visible. The Enterprise Admin or user is able to view the chatbot widget exactly as it will appear on a real webpage.If a new image is uploaded, it replaces the existing one.
Message Validation Table: 
ScenarioUser ActionSystem ResponseUpload correct imageUploads .jpg/.png ≤ 5 MBImage uploaded successfully, confirmation message displayed as a toaster messageUpload incorrect formatUploads .gif or .pdfError message: "Only .jpg and .png formats are supported."Upload oversized imageUploads > 5 MBError message: "File size exceeds 5 MB limit."Replace existing imageUploads new valid imageConfirmation: "New image successfully uploaded. Previous image has been replaced."Click "Experience on a website" with valid imageClick optionPreview opens with background image and fully functional widget.Click "Experience on a website" without uploading imageClick optionSystem opens preview with default placeholder background and widget.

---

## Comments

_No comments_