# CPAAS-41855: If the user adds multiple questions, the previous question gets hidden; we should provide a scroller for a better user experience.

**Status:** To Do  
**Priority:** Medium

---

## Description

ISSUE SUMMARY: If the user adds multiple questions, the previous question gets hidden; we should provide a scroller for a better user experience.Steps To Reproduce:-Log in to the Neuratalk portal  with valid EA/EU credentialsUser is redirected successfully to the Neuratalk homepage.Now, click on the Neuratalk AI module.Now, click on the edit icon on any bot present on the page or click on the create button.Now, click on the Train tab.Navigate to the FAQ subtab.Click on the Add question button.Add any question.Now, click on the +Add button to add multiple alternate questions.Check the previously added question.Expected Results:-If the user adds multiple questions, all the questions should be visible on the pop-up.Actual Results:-The previous question becomes hidden if the user adds multiple alternative questions.ADDITIONAL DETAILS:
Browser & Version - Google Chrome Version 106.0.5249.103
Any Error/Warning in Console/Network tab/Logs -   NA
Other Environments where this issue exists -  NA
Any similar issue reported in the past -   NA
Any other behaviour to be taken into consideration by <PERSON> before fixing this bug -  NATEST PROOFS:In XD-In UI-

---

## Comments

_No comments_