# CPAAS-41206: UX| Mutlilanguage feature implemented in the ADD UTTERANCE module is not user friendly

**Status:** To Do  
**Priority:** Medium

---

## Description

Pre-requisite:You must be logged in with a valid Enterprise Admin/User.Atleast 1 chatbot should be there in the systemAtleast 1 intent should be there with chatbot without any utteranceYou must be on the ‘Train' → 'Intent Utterances’ tabSteps to Reproduce:Login to NeuraTalk portalNavigate to NeuraTalk AI pageClick on any of the listed chatbotClick on Train tabSelect Intent Utterances tabSelect the listed intent Click on ADD UTTERANCE button Add one utterance in english languageClick on overflow menu and select EditChange the language for the utterance and save itObserve the listingObservation:When user filter the English language utterance it display utterance in different languageWhen user deletes or edit/delete the utterance from the filtered listing he is in impression of editing/deleting utterance of the listed one, but actually he is editing the utterance of the filtered language

---

## Comments

### Comment by nitin.jha:
Will have to discuss with  ayan about this UX issue