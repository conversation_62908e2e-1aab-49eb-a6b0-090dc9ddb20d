# CPAAS-41554: If the user drags and drops the node, removes the node from the canvas, the dots on the canvas become invisible, and if the user drags the nodes to the invisible area, the nodes are not added.

**Status:** To Do  
**Priority:** Medium

---

## Description

Description: If the user drags and drops the node, removes the node from the canvas, the dots on the canvas become invisible, and if the user drags the nodes to the invisible area, the nodes are not added.Pre-requisite:
NeuraTalk AI bot builder page implementation is doneSteps to reproduce:Log in to CPaaS  with Enterprise admin credentialsNow, navigate to NeuraTalk AI Click on the CREATE buttonOn the node palette, drag any node to the canvas.Now remove the node from the canvas.Check the dots on the canvas.Now drag the node to the invisible dots area.Now, reload the page.Check the dots on the canvas.Expected Result:The drag and drop should be smooth, and the dots should be invisible on the canvas.Actual Result:The dots on the canvas become invisible if the user removes the node, also if they reload the page, the dots are visible on half of the canvas.Evidences:-

---

## Comments

_No comments_