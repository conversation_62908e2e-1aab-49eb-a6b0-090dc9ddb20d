# CPAAS-40808: [FAQ]: No offline error message is displayed when attempting to create an FAQ category without an internet connection, leading to silent failure

**Status:** To Do  
**Priority:** High

---

## Description

Other observation:
This issue is also observed while adding questions and answers via the ADD QUESTION modalDescription: When a user's device is disconnected from the internet and they attempt to create a new FAQ category, the application fails to handle the resulting network error. No error message or offline notification is shown to the user.Pre-requisite:Valid Enterprise Admin/User should existsSteps to Reproduce:Log in to the CPAAS application.Navigate to the 'Neuratalk AI 'pageClick on any existing bot or click on the ‘CREATE’ buttonNavigate to ‘Train' → 'FAQs’ tabDisconnect your device from the internet. (e.g., turn off Wi-Fi or use browser developer tools to simulate an offline state).Click the '+' icon to add a new category.Enter a name for the category, such as "Offline Test".Click the "Save" button.Observe the application's feedback. Note the absence of an error message.Expected Results: Immediately after clicking "Save" while offline, the application should detect the network request failure. It should display a clear error message to the userActual Results: No error message is displayed. The application fails silently.Attachments:

---

## Comments

_No comments_