# CPAAS-42051: Debugger | Multiple UI issues on the debugger console.

**Status:** To Do  
**Priority:** Minor

---

## Description

Description: The design of the logs in the console does not match the XD.If there are any error logs in the console, the status code should also be visible.The debugger page should not overlap the left menu bar.Label is missing on all the logs, exp(error: Info:)All the text in the logs should be in black color, and then the highlight should be as per the status of the log.The icons for each log do not match the XD. Please provide a proper icon as per XD.Pre-requisite:
NeuraTalk AI bot builder page implementation is doneSteps to reproduce:Log in to CPaaS  with Enterprise admin credentialsNow, navigate to NeuraTalk AI Click on the CREATE buttonNow, click on the design tab.Create a flow on the canvas.Click on the build icon after creating the flow.Now click on the preview icon.On the preview page, click on the bug icon to open the console panel.Chat with the bot to get the logs in the console.Check the design and text on the console.Expected Result:The design and color of the log items should match the XD. Actual Result:The design and color is not matching with XD.Evidence:-

---

## Comments

_No comments_