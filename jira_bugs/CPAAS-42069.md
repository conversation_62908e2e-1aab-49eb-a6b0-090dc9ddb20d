# CPAAS-42069: [FAQ]: Unable to add category with valid names

**Status:** To Do  
**Priority:** High

---

## Description

Description: Unable to add category with valid namePre-requisite:Valid Enterprise Admin/User should existsSteps to Reproduce:Log in to the CPAAS application.Navigate to the 'Neuratalk AI 'pageClick on any existing bot or click on the ‘CREATE’ buttonNavigate to ‘Train' → 'FAQs’ tabClick on the '+' icon to add a categoryEnter a unique, valid name for the category as below:123Category_  _CategoryClick on Add button and observeExpected Results: The new category should be created, saved, and immediately displayed in the UI.Actual Results: The category is not created.Attachments:

---

## Comments

### Comment by <PERSON><PERSON>:
As per confirmation from  ,
',-_&()[]{}: characters are allowed as well.
cc: