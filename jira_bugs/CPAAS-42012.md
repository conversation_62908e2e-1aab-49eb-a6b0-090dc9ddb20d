# CPAAS-42012: NeuraTalk| "No flows connected" badge keeps appearing with intent even after connecting intent to triggers

**Status:** To Do  
**Priority:** High

---

## Description

Description: "No flows connected" badge keeps appearing with intent even after connecting intent to triggersPre-requisite:
Intent implementation is doneSteps to reproduce:Log in to CPaaS  with Enterprise admin credentialsNow, navigate to NeuraTalk AI Click on Create buttonOn the chatbot builder click Train tabSelect INTENT UTTERANCES sub-tabClick on ADD INTENT buttonFill the details and ADD the intent Add some utterances in the intentIn the design tab add a Trigger nodeClick on Trigger node and link it with created intentNavigate to Train tab and observe the badgeExpected Result:"No flows connected" badge is removedActual Result:"No flows connected" badge is still appearing with the intent

---

## Comments

_No comments_