# CPAAS-35700: As an Enterprise Admin or user, I shall be able to Edit/Delete/Duplicate/Export a Chatbot from the Landing Page

**Status:** To Do  
**Priority:** Minor

---

## Description

As an Enterprise Admin or user, I shall be able to Edit/Delete/Duplicate/Export a Chatbot from the Landing PageAs an Enterprise Admin or user, when I am on the landing page, I shall be able to click on the overflow menu against every chatbot card grid and get the options of Delete, Duplicatr and Export in the menu. Moreover, I shall be able to click on the 'edit' icon on the card, which will take
me inside the chatbot to congifure/set-up the chatbot. I shall be able to operate these opreations on both the Live and Draft chatbots.  PRD:  Wireframe:  
Refer Web 1366 – 15 Screen Pre-Condition: Chatbot should be successfully created by the EA or the EU. Workflow: Step 1: Click on the overflow (3-dots) menu of any chatbot (either Live or Draft)Step 2: Choose the option:Delete: If the chatbot is in the Draft stage, I get a pop-up asking 'Are your sure you want to delete the chatbot?'No, Cancel: If I press this option, I cancel the delete operation and land back to the landing page keeping the state of the chatbot unchanged.Yes, Delete: If I press this option, it deletes the chatbot from the database and landing page GUI.Edit: If I click on this option, I am taken inside the particular chatbot and I see the Default Tab open by default. I land on to the flow which I was last working on.
Duplicate: If I click on this option, it duplicates all the contents and the configuration of the chatbot and creates a copy of the same. However, this wouldn't copy the chatbot in 'Live' state. It will be copied as a draft chatbot.
Export: If I click on this option, a JSON file is exported and downloaded automatically.Post-Condition: Delete: The chatbot should not be visible in the GUI and database. It should not appear when searched for using the search bar. Edit: As an EA or EU, I should land on to the Design tab of the particular chatbot. Duplicate: Another chatbot is created in the 'Draft' stage and appears on the landing page. E.g. If I have duplicated a chatbot named, 'Healthcare',
it should be created with the name of 'copyof_Healthcare'. Export: As an EA or EU, I should be able to see the JSON structure of the chatbot as an exported .txt file.

---

## Comments

_No comments_