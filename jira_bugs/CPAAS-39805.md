# CPAAS-39805: NeuraTalk| UI issues of Add Intent page of chatbuilder

**Status:** To Do  
**Priority:** Medium

---

## Description

Description: The sub-tab name should be 'Intent Utterances' instead of 'Utterances' on the Train tab of chatbot builderPre-requisite:
Intent implementation is doneISteps to reproduce:Log in to CPaaS  with Enterprise admin credentialsNow, navigate to NeuraTalk AI Click on Create buttonOn the chatbot builder click Train tabObservations:Intent text on top left corner of main content area is missingStart adding intents font size in 18 px instead of 16 pxNothing to show font size is 16 px instead of 14 pxOn the Add Intent pop-up ADD INTENT font size is 16 px instead of 14 pxIn XD the input field design on ADD INTENT form is different. In the XD 'Intent Name' label is missing and placeholder is implemented where as in the actual implementation it is vice-versa.Validation message for invalid input in the ADD INTENT form is missingADD button enables when user enter spaces in the Intent Name field of ADD INTENT popup

---

## Comments

_No comments_