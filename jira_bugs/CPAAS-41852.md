# CPAAS-41852: UI should show a validation message if the user hits the save button multiple times.

**Status:** To Do  
**Priority:** Medium

---

## Description

ISSUE SUMMARY: UI should show a validation message if the user hits the save button multiple times.Steps To Reproduce:-Log in to the Neuratalk portal  with valid EA/EU credentialsUser is redirected successfully to the Neuratalk homepage.Now, click on the Neuratalk AI module.Now, click on the edit icon on any bot present on the page or click on the create button.Now, click on the Settings tab.Select the language card on the settings page.Select any language.Click on the save button.Again, click on the save button.Expected Results:-A validation message should be displayed upon clicking the save button multiple times.Actual Results:-No validation message is displayed if the user clicks on the save button multiple times.ADDITIONAL DETAILS:
Browser & Version - Google Chrome Version 106.0.5249.103
Any Error/Warning in Console/Network tab/Logs -   NA
Other Environments where this issue exists -  NA
Any similar issue reported in the past -   NA
Any other behaviour to be taken into consideration by <PERSON> before fixing this bug -  NATEST PROOFS:

---

## Comments

_No comments_