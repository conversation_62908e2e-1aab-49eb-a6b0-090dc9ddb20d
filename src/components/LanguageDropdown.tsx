import React, { useEffect, useMemo, useState } from 'react';
import { useGetBotLanguagesQuery, useGetLanguagesQuery } from '@/store/api';
import DropdownButton from './dropdownButton';
import { Language } from '@/types';
import { useBotIdParam } from '@/hooks/useRouterParam';

interface IProps {
  onChange: (langId: string, languageNode: Language) => void;
  initialValue?: string;
}

const LanguageDropdown: React.FC<IProps> = ({ onChange, initialValue }) => {
  const { botId } = useBotIdParam();
  const [selectedLanguage, setSelectedLanguage] = useState<Language>();

  const { data: botLanguagesData } = useGetBotLanguagesQuery({ filter: { botId: { eq: botId } } });
  const botLanguages = botLanguagesData?.data?.items || [];

  const { data: languagesData } = useGetLanguagesQuery(
    {
      filter: {
        id: {
          in: botLanguages.map(botLanguage => botLanguage.langId),
        },
      },
    },
    {
      skip: !botLanguages.length,
    }
  );

  const botDefaultLanguage = useMemo(() => {
    return botLanguages.find(botLanguage => botLanguage.isDefault);
  }, [botLanguages]);

  const languageList = languagesData?.data?.items || [];

  const languages = useMemo(
    () =>
      languageList.map(language => {
        return {
          value: language?.id ?? '',
          label: language?.name ?? '',
        };
      }),
    [languageList]
  );

  useEffect(() => {
    if (languageList?.length && !selectedLanguage) {
      const languageNode =
        //TODO: rm this code check
        languageList.find(
          language =>
            language.id === initialValue ||
            language.id === botDefaultLanguage?.langId ||
            language.code === 'en'
        ) ?? languageList[0];
      setSelectedLanguage(languageNode);
      onChange?.(languageNode.id, languageNode);
    }
  }, [languageList, selectedLanguage]);

  const onChangeHandler = (langId: string) => {
    const languageNode = languageList.find(language => language.id === langId);
    setSelectedLanguage(languageNode);
    onChange(langId, languageNode!);
  };

  return (
    <DropdownButton
      options={languages ?? []}
      value={selectedLanguage?.id ?? ''}
      onChange={onChangeHandler}
      triggerClassName=""
    />
  );
};

export default LanguageDropdown;
