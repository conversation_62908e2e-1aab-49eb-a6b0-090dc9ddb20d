import React from 'react';
import { Check } from 'lucide-react';

interface SuccessToastMessageProps {
  message: string;
}

const SuccessToastMessage: React.FC<SuccessToastMessageProps> = ({ message }) => {
  return (
    <div className="flex items-center gap-2">
      <Check className="h-5 w-5" />
      <span className='font-normal'>{message}</span>
    </div>
  );
};

export default SuccessToastMessage;