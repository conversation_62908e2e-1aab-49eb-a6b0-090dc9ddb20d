// Filter types (matching common package)
export interface FilterCondition {
  eq?: any;
  ne?: any;
  neq?: any;
  gt?: number | Date;
  gte?: number | Date;
  lt?: number | Date;
  lte?: number | Date;
  like?: string;
  in?: any[];
  notIn?: any[];
  isNull?: boolean;
  isNotNull?: boolean;
}
export interface FilterObject {
  [column: string]: FilterCondition;
}

// Query params

export enum OrderDirection {
  ASC = 'ASC',
  DESC = 'DESC',
}
export type OrderItem = [string, OrderDirection];
export type OrderArray = OrderItem[];

export interface PaginationParams {
  page?: number;
  search?: string;
  limit?: number;
  order?: OrderArray;
  filter?: FilterObject;
}

export interface UuidParams {
  id: string;
}

// Response types
export interface PaginatedResponse<T> {
  items: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}
