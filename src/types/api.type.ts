import { FlowVersion } from './index';

export interface ErrorDetails {
  validation: string;
  code: string;
  message: string;
  path: string[];
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: ErrorDetails[];
  };
  timestamp: string;
}

export interface ApiRTKReject<T = any> {
  data: ApiResponse<T>;
  status: number;
}
