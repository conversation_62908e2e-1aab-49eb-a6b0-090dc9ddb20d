import * as React from 'react';
import { Search, LayoutGrid, List, Funnel } from 'lucide-react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import ChatbotCard from './ChatbotCard';
import DropdownButton from '@/components/dropdownButton';
import EditorLoader from '@/components/editor-loader';
import MicroFrontendWrapper from '@/components/MicroFrontendWrapper';
import {
  useGetBotsQuery,
  useCreateBotMutation,
  useDeleteBotMutation,
  useCloneBotMutation,
} from '@/store/api/chatBotApi';
import { dateOptions, getNTBTab, RoutesName } from '@/lib/constant';
import { DateFilter, StatusFilter } from '@/types/enums/enums';
import group from '../../assets/icons/header-banner.svg';
import { getDateRange, getDaysAgo } from '@/utils/getDaysRange';
import { Bot, FilterOptions, OrderDirection } from '@/types';
import {
  PaginationEmptyState,
  PaginationError,
  PaginationLoader,
  PaginationProvider,
  PaginationRenderItems,
  PaginationSearch,
  usePagination,
} from '@/components/Pagination';
import { useToast } from '@/hooks/use-toast';
import SuccessToastMessage from '@/components/SuccessToastMessage';
import { getCurrentTimestamp } from '@/utils/getCurrentTimeStamp';

// Sub-components
const HeaderSection: React.FC = () => {
  const { t } = useTranslation();
  return (
    <section className="relative flex h-[200px] shadow-none overflow-hidden">
      <img
        src={group}
        alt="NeuraTalk AI Background"
        className="absolute -top-[16.4rem] left-0 w-full object-fill h-auto z-0"
      />
      <div className="absolute bottom-0 left-0 w-full h-6 bg-gradient-to-b from-transparent to-white z-10" />
      <div className="absolute inset-0 w-1/2 flex flex-col justify-center px-6 top-6 left-0">
        <p className="text-tertiary-600 text-base max-w-3xl">
          <span className="font-medium text-tertiary-900 text-base">{t('home.title')} </span>
          {t('home.description')}
        </p>
      </div>
    </section>
  );
};

const FilterSection: React.FC<{
  filterOptions: FilterOptions;
  setFilterOptions: React.Dispatch<React.SetStateAction<FilterOptions>>;
}> = ({ filterOptions, setFilterOptions }) => {
  const { t } = useTranslation();

  return (
    <div className="flex flex-col sm:flex-row items-center gap-3 w-full md:w-auto flex-wrap">
      <div className="relative sm:w-auto md:w-52">
        <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-slate-400" />
        <PaginationSearch
          placeholder={t('common.search')}
          className="pl-10 pr-3 py-2 h-10 border border-slate-300 rounded-md w-full bg-background shadow-sm text-sm placeholder-tertiary-400"
          debounceMs={500}
        />
      </div>
      <DropdownButton
        value={filterOptions.dateFilter}
        onChange={(value: FilterOptions['dateFilter']) =>
          setFilterOptions(prev => ({ ...prev, dateFilter: value }))
        }
        options={[...dateOptions]}
        placeholder={t('common.date')}
        className="min-w-36 text-tertiary-500 border-slate-300 border rounded-md flex justify-between items-center"
        triggerClassName="bg-background"
      />
      <Button
        variant="outline"
        className="bg-background shadow-sm border-tertiary-300 text-slate-500 hover:bg-tertiary-50 w-full sm:w-auto h-10"
        disabled={true}
      >
        <Funnel className="h-4 w-4 mr-2 text-tertiary--500" />
        {t('common.filter')}
      </Button>
    </div>
  );
};

const ActionButtons: React.FC<{ onCreateChatbot: () => void }> = ({ onCreateChatbot }) => {
  const { t } = useTranslation();
  return (
    <div className="flex items-center gap-2 w-full sm:w-auto justify-start md:justify-end">
      <Button
        variant="outline"
        size="icon"
        className="bg-background text-primary-600 border-none hover:bg-primary-100 shadow-md h-10 w-10"
      >
        <LayoutGrid className="h-5 w-5" />
      </Button>
      <Button
        size="icon"
        className="bg-transparent text-tertiary-500 hover:border-tertiary-300 hover:bg-tertiary-200 hover:shadow-sm h-10 w-10"
        disabled={true}
      >
        <List className="h-5 w-5" />
      </Button>
      <Button
        className="bg-primary-500 hover:bg-primary-600 text-background shadow-sm px-6 py-2 h-10 text-sm rounded-md"
        onClick={onCreateChatbot}
      >
        {t('common.create')}
      </Button>
    </div>
  );
};

const ChatbotList: React.FC<{
  chatbots: Bot[];
  onDelete: (id: string) => void;
  onClone: (id: string) => void;
}> = ({ chatbots, onDelete, onClone }) => {
  const { t } = useTranslation();

  return (
    <section className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 p-5">
      {chatbots.map(bot => (
        <ChatbotCard
          key={bot.id}
          id={bot.id}
          title={bot.name}
          description={bot.description}
          status={bot.status}
          lastUpdated={getDaysAgo(bot?.updatedAt ?? '')}
          onDelete={onDelete}
          onClone={onClone}
        />
      ))}
    </section>
  );
};

// Main Component
const Home: React.FC = () => {
  const { t } = useTranslation();
  const { toast } = useToast();
  const navigate = useNavigate();
  const [filterOptions, setFilterOptions] = React.useState<FilterOptions>({
    dateFilter: DateFilter.All,
    statusFilter: StatusFilter.All,
    searchTerm: '',
  });

  const { lowerBound: lb, upperBound: ub } = getDateRange(filterOptions.dateFilter);
  const lowerBound = lb ? new Date(lb).toISOString() : undefined;
  const upperBound = ub ? new Date(ub).toISOString() : undefined;

  const filter: any = {
    status:
      filterOptions.statusFilter !== StatusFilter.All
        ? { eq: filterOptions.statusFilter }
        : { ne: 'inactive' },
    ...(lowerBound || upperBound
      ? {
          updatedAt: {
            ...(lowerBound && { gte: lowerBound }),
            ...(upperBound && { lte: upperBound }),
          },
        }
      : {}),
  };

  const pagination = usePagination({
    queryHook: query =>
      useGetBotsQuery(
        {
          search: filterOptions.searchTerm,
          order: [['updatedAt', OrderDirection.DESC]],
          filter,
          ...query,
        },
        { skip: !filter }
      ),
  });

  const {
    queryState: { data: botsResponse, isFetching, error },
  } = pagination;

  const [createBot, { isLoading: isLoadingCreateBot }] = useCreateBotMutation();
  const [deleteBot] = useDeleteBotMutation();
  const [cloneBot] = useCloneBotMutation();

  const handleCreateChatbot = async () => {
    const title = `${t('chatbot.untitled')}_${getCurrentTimestamp()}`;
    try {
      const res = await createBot({
        title,
        domain: 'Other',
        description: t('chatbot.noDescription'),
      }).unwrap();
      navigate(getNTBTab(res.data!.id!, 'Design'));
    } catch (error: any) {
      console.error('Error creating bot:', error);
      toast({
        title: t('common.error'),
        description: error,
        variant: 'destructive',
      });
    }
  };

  const handleDeleteChatbot = async (id: string) => {
    try {
      await deleteBot({ id }).unwrap();
      toast({ title: <SuccessToastMessage message={t('chatbot.chatbotDeleted')} /> });
    } catch (error: any) {
      console.error('Error deleting bot:', error);
      toast({
        title: t('common.error'),
        description: error,
        variant: 'destructive',
      });
    }
  };

  const handleCloneChatbot = async (id: string) => {
    try {
      const { id: newId } = await cloneBot({ botId: id }).unwrap();
      toast({ title: <SuccessToastMessage message={t('home.chatbotCloned')} /> });
      navigate(RoutesName.NEURA_TALK_BUILDER.replace(':botId', newId));
    } catch (error: any) {
      console.error('Clone failed:', error);
      toast({
        title: t('common.error'),
        description: error,
        variant: 'destructive',
      });
    }
  };

  if (isLoadingCreateBot) return <EditorLoader />;

  return (
    <div className="min-h-screen">
      <HeaderSection />
      <PaginationProvider value={pagination}>
        <div className="flex flex-col px-6 mb-40 mx-auto max-w-7xl">
          <section className="flex flex-col md:flex-row justify-between items-center mb-8 gap-4">
            <FilterSection filterOptions={filterOptions} setFilterOptions={setFilterOptions} />
            <ActionButtons onCreateChatbot={handleCreateChatbot} />
          </section>
          <PaginationRenderItems<Bot>
            renderItems={items => (
              <ChatbotList
                chatbots={items}
                onDelete={handleDeleteChatbot}
                onClone={handleCloneChatbot}
              />
            )}
          />
          <PaginationEmptyState>
            <p className="col-span-full text-center text-tertiary-500 py-10">
              {t('home.noResults')}
            </p>
          </PaginationEmptyState>
          <PaginationLoader />
          <PaginationError />
        </div>
      </PaginationProvider>
    </div>
  );
};

export default function HomeWrapper() {
  return (
    <MicroFrontendWrapper>
      <Home />
    </MicroFrontendWrapper>
  );
}
