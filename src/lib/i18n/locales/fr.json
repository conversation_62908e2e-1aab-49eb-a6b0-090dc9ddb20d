{"common": {"search": "<PERSON><PERSON><PERSON>", "filter": "<PERSON><PERSON><PERSON>", "create": "CRÉER", "save": "ENREGISTRER", "submit": "So<PERSON><PERSON><PERSON>", "cancel": "ANNULER", "delete": "<PERSON><PERSON><PERSON><PERSON>", "add": "AJOUTER", "clone": "<PERSON><PERSON><PERSON>", "export": "Exporter", "edit": "Modifier", "yes": "OUI", "no": "NON", "selectOption": "Sélectionnez une option", "getStarted": "COMMENCER", "preview": "Prévisualiser", "publish": "PUBLIER", "duplicate": "<PERSON><PERSON><PERSON><PERSON>", "versionHistory": "Historique des versions", "flows": "Flux", "debugger": "D<PERSON><PERSON>gueur", "message": "Message", "image": "Image", "file": "<PERSON><PERSON><PERSON>", "video": "Vidéo", "addViaUrl": "Ajouter via URL", "enterFileUrl": "Entrez l'URL du fichier", "maxSize": "<PERSON><PERSON> maximale : {{size}} Mo", "clickOrDrag": "<PERSON><PERSON>z ou faites glisser le fichier {{type}} ici", "clickOrDragFiles": "C<PERSON>z ou faites glisser le fichier dans cette zone pour l'envoyer", "writeMessage": "Écrire un message", "typeMessage": "Tapez votre message...", "fillAboveField": "Remplissez le formulaire ci-dessus pour continuer", "dateRange": "Choisissez une plage de dates", "trackOrder": "Suivre ma commande", "cancelOrder": "Annuler ma commande", "chatWithAgent": "Discuter avec un agent", "viewSimilarProducts": "Voir des produits similaires", "hello": "Bon<PERSON><PERSON>, {{name}} !", "howCanIHelp": "Comment puis-je vous aider aujou<PERSON>'hui ?", "searchFlows": "Rechercher des flux...", "onboarding": "Intégration initiale", "notFound": "Non trouvé", "enterValidValue": "Veuillez entrer une valeur valide", "translateTo": "Traduire en", "translate": "TRADUIRE", "nothingToShow": "<PERSON><PERSON> afficher", "generate": "<PERSON><PERSON><PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>", "nodeId": "ID du nœud :", "noData": "<PERSON><PERSON><PERSON> don<PERSON>", "searchEllipsis": "Rechercher...", "justNow": "à l'instant", "update": "Mettre à jour", "error": "<PERSON><PERSON><PERSON>", "somethingWrong": "<PERSON><PERSON><PERSON> chose s'est mal passé", "saveChanges": "ENREGISTRER LES MODIFICATIONS", "date": "Date", "loading": "Chargement...", "build": "Construire", "import": "Importer", "share": "Partager", "goBack": "Retour", "noNode": "<PERSON><PERSON><PERSON> nœud trouvé", "versions": "Versions", "intent": "Intention"}, "chatbot": {"botPublishedSuccessfully": "Bot publié avec succès !", "untitled": "Sans titre", "noDomain": "PAR DÉFAUT", "noDescription": "Sans description", "confirmDelete": "CONFIRMER LA SUPPRESSION", "deleteMessage": "Êtes-vous sûr de vouloir supprimer ce chatbot ?", "chatbotDeleted": "Chatbot supprimé avec succès", "noCancel": "NON, ANNULER", "yesDelete": "OUI, SUPPRIMER", "newChatbotPrefix": "Nouveau-Chatbot", "cloneFailed": "Échec du clonage du chatbot.", "buildSuccess": "Bot construit avec succès !", "buildFailed": "Échec de la construction du bot.", "buildStreamError": "Erreur de connexion au flux de construction.", "defaultTitle": "<PERSON>", "defaultDomain": "E-commerce", "defaultDescription": "Aide les clients à naviguer dans le processus d'achat numérique."}, "debugger": {"logs": "<PERSON><PERSON><PERSON>", "aiAnalysis": "Analyse IA", "sessionData": "<PERSON><PERSON><PERSON> de <PERSON>", "aiAnalysisContent": "Contenu de l'analyse IA à venir.", "sessionDataContent": "Contenu des données de la session à venir.", "noAiAnalysisLogs": "Aucun journal d'analyse IA disponible.", "noSessionDataLogs": "Aucun journal de données de session disponible.", "noLogs": "Aucun journal disponible."}, "preview": {"confirmDialog": "Voulez-vous mettre fin à cette conversation ?", "confirmDialogDesc": "<PERSON><PERSON> effacera le chat et fermera la fenêtre"}, "validation": {"maxLength": "Ce champ ne peut pas dépasser {{count}} caractères.", "BLANK_URL": "L'URL ne peut pas être vide.", "URL_TOO_LONG": "L'URL est trop longue.", "INVALID_URL": "Format d'URL invalide.", "URL_DISPLAY_TEXT_TOO_LONG": "Le texte affiché ne peut pas dépasser 100 caractères.", "invalidPhoneNumber": "Numéro de téléphone invalide pour {{field}}.", "fieldRequired": "{{field}} est requis.", "invalidEmail": "E-mail invalide pour {{field}}.", "passwordMinLength": "Le mot de passe pour {{field}} doit comporter au moins 6 caractères.", "invalidTimeFormat": "Format d'heure invalide pour {{field}}.", "invalidDate": "Date invalide pour {{field}}.", "pastDateRequired": "{{field}} doit être une date passée (avant {{date}})", "futureDateRequired": "{{field}} doit être une date future (après {{date}})", "invalidNumber": "Numéro invalide pour {{field}}"}, "home": {"title": "NeuraTalk AI", "description": "est une solution d'IA conversationnelle de pointe conçue pour améliorer l'engagement des clients, automatiser le support et rationaliser les opérations commerciales.", "noResults": "aucun résultat", "lastUpdated": "<PERSON><PERSON><PERSON> mise à jour le {{date}}"}, "editor": {"chatbotName": "<PERSON>m du Cha<PERSON>bot", "domain": "Domaine", "description": "Description", "uploadImage": "C<PERSON>z ou faites glisser un fichier dans cette zone pour l'envoyer", "uploadFormat": "(Taille : <PERSON>sq<PERSON>'<PERSON> 5 Mo | Format : jpg, png)", "unsupportedFile": "Type de fichier non pris en charge", "fileTooLarge": "Le fichier doit être inférieur à 2 Mo", "invalidName": "Seuls les lettres, chiffres, tirets (-), traits de soulignement (_), et points (.) sont autorisés", "invalidImageFile": "Veuillez envoyer un fichier image valide (png, jpg, jpeg, webp, gif, svg)", "nameRequired": "Le nom du chatbot est requis", "nameMaxError": "Le nom du chatbot ne peut pas dépasser 50 caractères", "domainRequired": "Le domaine est requis", "descMaxError": "La description ne peut pas dépasser 150 caractères", "updateSuccess": "<PERSON>t<PERSON> mis à jour avec succès", "descRequired": "La description est requise", "updateError": "Échec de la mise à jour du bot", "writeMessage": "Écrire un message"}, "navigation": {"neuraTalk": "NeuraTalk", "create": "<PERSON><PERSON><PERSON>"}, "domains": {"ecomm": "E-commerce", "telecom": "Télécom", "retail": "Vente au détail", "travel": "Voyage", "other": "<PERSON><PERSON>"}, "emptyState": {"title": "Rien ici pour l'instant", "description": "Aucun contenu à afficher pour le moment."}, "intents": {"title": "Intentions", "addTitle": "AJOUTER UNE INTENTION", "editTitle": "MODIFIER UNE INTENTION", "name": "Nom de l'intention", "namePlaceholder": "Nom de l'intention", "nameLabel": "Nom de l'intention", "nameRequired": "Le nom de l'intention est requis.", "startAdding": "Commencez à ajouter des intentions", "noFlowsConnected": "Aucun flux connecté", "selectToManage": "Sélectionnez une intention pour gérer les expressions", "loading": "Chargement des intentions.", "loadingError": "<PERSON><PERSON>ur lors du chargement des intentions.", "intentAdded": "Intention ajoutée avec succès.", "intentUpdated": "Intention mise à jour avec succès.", "intentDeleted": "Intention supprimée avec succès.", "confirmDeleteTitle": "CONFIRMER LA SUPPRESSION DE L'INTENTION", "deleteConfirmationMessage": "Êtes-vous sûr de vouloir supprimer cette intention ?", "utterances": {"title": "Expressions", "addTitle": "AJOUTER UNE EXPRESSION", "editTitle": "MODIFIER UNE EXPRESSION", "enterPlaceholder": "Entrez l'expression", "startAdding": "Commencez à ajouter des expressions", "emptyError": "L'expression ne peut pas être vide.", "loading": "Chargement des expressions.", "loadingError": "Erreur lors du chargement des expressions.", "utteranceAdded": "Expression ajoutée.", "utteranceUpdated": "Expression mise à jour.", "utteranceDeleted": "Expression supprimée.", "confirmDeleteTitle": "CONFIRMER LA SUPPRESSION DE L'EXPRESSION", "deleteConfirmationMessage": "Êtes-vous sûr de vouloir supprimer cette expression ?"}}, "entities": {"title": "Entités", "addTitle": "AJOUTER UNE ENTITÉ", "entityName": "Nom de l'entité", "entityNamePlaceholder": "Nom de l'entité", "type": "Type", "selectType": "Sélectionner le type", "enablePartialMatch": "Activer la correspondance partielle", "startAdding": "Commencez à ajouter des entités", "loading": "Chargement des entités...", "error": "Erreur lors du chargement des entités.", "noEntitiesFound": "Aucune entité trouvée", "searchEntities": "Rechercher des entités...", "selected": "Sélectionné", "removeEntity": "Supprimer l'entité", "types": {"text": "Texte", "list": "Liste", "regex": "REGEX"}, "table": {"name": "Nom", "type": "Type", "value": "<PERSON><PERSON>", "action": "Action"}, "validation": {"nameRequired": "Le nom de l'entité est requis.", "typeRequired": "Le type de l'entité est requis.", "valueRequired": "La valeur est requise."}, "addValue": "Ajouter une valeur", "editTitle": "MODIFIER UNE ENTITÉ", "regexValuePlaceholder": "Valeur Regex", "entityAdded": "Entité ajoutée avec succès.", "entityUpdated": "Entité mise à jour avec succès.", "entityDeleted": "Entité supprimée avec succès.", "confirmDeleteTitle": "CONFIRMER LA SUPPRESSION DE L'ENTITÉ", "deleteConfirmationMessage": "Êtes-vous sûr de vouloir supprimer cette entité ?"}, "train": {"entities": {"title": "Entités", "content": "Contenu des entités", "addTitle": "AJOUTER UNE ENTITÉ", "nameLabel": "Nom de l'entité", "intentIdLabel": "ID de l'intention", "metadataLabel": "Métadonnées (JSON)", "metadataPlaceholder": "Entrez les métadonnées au format JSON", "loading": "Chargement des entités...", "error": "Erreur lors du chargement des entités.", "validation": {"nameRequired": "Le nom de l'entité est requis.", "intentIdRequired": "L'ID de l'intention est requis.", "invalidJson": "Format JSON invalide pour les métadonnées."}}, "synonyms": {"title": "Synonymes", "content": "Contenu des synonymes"}, "smallTalk": {"title": "Conversation informelle", "content": "Contenu de la conversation informelle"}, "trainFromLogs": {"title": "Entraîner à partir des journaux", "content": "Contenu de l'entraînement à partir des journaux"}, "tabs": {"intentUtterances": "Expressions de l'intention", "entities": "Entités", "faqs": "Questions fréquentes", "synonyms": "Synonymes", "smallTalk": "Conversation informelle", "trainFromLogs": "Entraîner à partir des journaux"}}, "faqs": {"title": "Questions & Réponses", "category": {"title": "<PERSON><PERSON><PERSON><PERSON>", "addTitle": "AJOUTER UNE CATÉGORIE", "editTitle": "MODIFIER UNE CATÉGORIE", "nameLabel": "Nom de la catégorie", "nameRequired": "Le nom de la catégorie est requis.", "startAdding": "Commencez à ajouter des catégories", "selectToManage": "Sélectionnez une catégorie pour gérer les questions", "categoryAdded": "Catégorie a<PERSON> avec succès.", "categoryUpdated": "Catégorie mise à jour avec succès.", "categoryDeleted": "Catégorie supprimée avec succès.", "confirmDeleteTitle": "CONFIRMER LA SUPPRESSION DE LA CATÉGORIE", "deleteConfirmationMessage": "Êtes-vous sûr de vouloir supprimer cette catégorie ?"}, "loading": "Chargement des FAQs.", "loadingError": "Erreur lors du chargement des FAQs.", "items": {"loading": "Chargement des éléments de FAQ...", "loadingError": "Erreur lors du chargement des éléments de FAQ.", "startAdding": "Commencez à ajouter des questions", "addTitle": "AJOUTER UNE QUESTION", "editTitle": "MODIFIER UNE QUESTION", "questionLabel": "Questions", "questionPlaceholder": "Entrez la question", "questionEmpty": "La question ne peut pas être vide.", "atLeastOne": "Au moins une question est requise.", "answerLabel": "Réponse", "answerPlaceholder": "Entrez la réponse", "answerEmpty": "La réponse ne peut pas être vide.", "linkFlowLabel": "Lier un flux", "chooseFlowPlaceholder": "Choisissez le <PERSON>", "primaryLabel": "Primaire", "questionPrefix": "Q", "answerPrefix": "R", "questionsAdded": "Questions ajoutées.", "questionsUpdated": "Questions mises à jour.", "maxQuestions": "V<PERSON> pouvez ajouter jusqu'à {{count}} questions.", "questionsDeleted": "Questions supprimées.", "confirmDeleteTitle": "CONFIRMER LA SUPPRESSION DE LA FAQ", "deleteConfirmationMessage": "Êtes-vous sûr de vouloir supprimer cet élément de FAQ ?"}, "validation": {"questionRequired": "La question est requise.", "atLeastOneQuestion": "Au moins une question est requise.", "answerRequired": "La réponse est requise."}}, "fileUpload": {"fileTooLarge": "Le fichier doit être inférieur à {{size}} Mo et être de type {{type}}", "someFilesRejected": "Certains fichiers ont été rejetés. Assurez-vous que les types sont corrects et que la taille est < {{size}} Mo.", "failedToUpload": "Échec de l'envoi : {{filename}}"}, "tabs": {"contentComingSoon": "Contenu de l'onglet {{tabName}} à venir"}, "builder": {"tabs": {"design": "Conception", "train": "<PERSON><PERSON><PERSON><PERSON>", "channels": "Canaux", "agentTransfer": "Transfert d'agent", "integrations": "Intégrations", "settings": "Paramètres"}}, "flows": {"untitledFlow": "fluxsantitre", "noFlows": "Aucun flux", "welcome": "Bienvenue", "fallback": "<PERSON><PERSON>", "targetFlow": "Flux cible", "existingFlow": "Mémoriser le contexte entre le flux de connexion", "errorLoading": "Erreur lors du chargement des flux", "newFlow": "Nouveau flux", "fetchError": "Échec de la récupération des flux", "flowDelete": "Flux supprimé avec succès", "flowNotCreated": "Impossible de créer le flux", "flowDeleted": "Flux supprimé avec succès", "flowNotDeleted": "Impossible de supprimer le flux", "flowDuplicated": "Flux dupliqué avec succès", "flowNotDuplicated": "Impossible de dupliquer le flux", "flowRenamed": "Flux renommé avec succès", "flowNotRenamed": "Impossible de renommer le flux"}, "agentTransfer": {"transfer": "Intégrez l'agent à partir de la page « Transfert d'agent » pour configurer l'agent natif", "selectAgentTransfer": "Sélectionnez un transfert d'agent", "nothingSelected": "<PERSON><PERSON>né", "filters": {"all": "Tous", "native": "<PERSON><PERSON>", "thirdParty": "Tiers"}, "tabs": {"available": "Disponible", "myAgentTransfers": "Mes transferts d'agent"}, "setupHeading": "Configurer", "setupDescription": "Fournissez les détails ci-dessous pour activer le support de {{agentTransferName}} pour le chatbot.", "generateToken": "GÉNÉRER UN JETON", "liveAgentPortalDetails": "<PERSON><PERSON><PERSON> du portail d'agent en direct", "pendingStatus": "En attente", "remove": "SUPPRIMER", "chatbotNameLabel": "Nom du chatbot :", "accessTokenLabel": "<PERSON><PERSON> d'accès :", "shareInstruction": "Partagez ce qui précède avec l'administrateur de l'agent pour l'activation."}, "settings": {"language": "<PERSON><PERSON>", "nlu": "NLU", "personalization": "Personnalisation", "llmConfiguration": "Configuration LLM", "cannedResponses": "Réponses prédéfinies", "loremDescription": "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor", "languages": "<PERSON><PERSON>", "yourLanguages": "<PERSON><PERSON>", "noLanguagesSelected": "Aucune langue sélectionnée", "availableLanguages": "Langues disponibles", "searchLanguages": "Rechercher des langues...", "defaultTag": "<PERSON><PERSON> <PERSON><PERSON>", "allSelectedLanguages": "Toutes les langues sélectionnées", "languagesSaved": "Langues enregistrées avec succès"}, "stencil": {"nodes": "<PERSON><PERSON><PERSON>", "searchNodes": "Rechercher des nœuds...", "engage": "Engager", "utilities": "Utilitaires", "marketplace": "Marketplace"}, "platform": {"web": "Web", "mobile": "Mobile"}, "pagination": {"previous": "Précédent", "next": "Suivant", "morePages": "Plus de pages", "loadingMore": "Chargement de plus...", "noItemsFound": "Aucun élément trouvé", "errorLoadingData": "Erreur lors du chargement des données", "tryAgain": "<PERSON><PERSON><PERSON><PERSON>", "defaultFilterUI": "Interface de filtre par défaut – personnalisable avec les props enfants"}, "form": {"loadingForm": "Chargement du formulaire...", "typing": "En train de taper...", "enterLabel": "Entrer une étiquette", "prompt": "Invite", "textField": "Champ de texte", "label": "Étiquette", "lableRequired": "L'étiquette est obligatoire", "promptRequired": "Une invite est requise"}, "errors": {"failedToSend": "Échec de l'envoi du message", "unexpectedResponse": "Réponse inattendue du serveur", "somethingWrong": "<PERSON><PERSON><PERSON> chose s'est mal passé"}, "channels": {"selectWABA": "Sélectionnez un numéro WABA pour vous connecter", "changeNumber": "CHANGER DE NUMÉRO", "webhook": "Webhook", "webhookInstruction": "Collez ce webhook contre le numéro WABA dans le canal NGAGE WhatsApp pour intégrer.", "switchToMeta": "Passer à l'API Meta Cloud", "switchDescription": "Passez à l'API Meta Cloud et liez votre chatbot via un partenaire BSP.", "switch": "CHANGER", "connect": "CONNECTER", "selectChannels": "Sélectionnez les canaux à configurer", "nothingSelected": "<PERSON><PERSON>né", "myChannels": "<PERSON><PERSON>", "whatsapp": "WhatsApp", "telegram": "Telegram", "voice": "Voix", "alexa": "Alexa", "available": "Disponible", "invalid": "INVALIDE", "testChannel": "Canal de test", "getStarted": "COMMENCER", "metaCloudAPI": "API Meta Cloud", "ngage": "NGAGE", "sms": "SMS", "virtualReceptionist": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "email": "E-mail", "rcs": "RCS", "chatbot": "<PERSON><PERSON><PERSON>", "network": "<PERSON><PERSON><PERSON>", "studio": "Studio", "allChannels": "To<PERSON> les canaux", "filters": {"all": "Tous", "native": "<PERSON><PERSON>", "text": "Texte", "voice": "Voix"}, "tabs": {"available": "Disponible", "myChannels": "<PERSON><PERSON>"}}, "nodes": {"agentTransfer": "Transfert d'agent", "appEnd": "Fin de l'application", "appStart": "Début de l'application", "choice": "<PERSON><PERSON>", "choiceOption": "Option de choix", "feedback": "Commentaires", "flowConnector": "Connecteur de flux", "http": "HTTP", "interactiveMessage": "Message interactif", "language": "<PERSON><PERSON>", "message": "Message", "notification": "Notification", "payment": "Paiement", "script": "<PERSON><PERSON><PERSON>", "text": "Texte", "waitDelay": "<PERSON><PERSON><PERSON> un délai", "whatsapp": "WhatsApp"}, "bots": {"testBot": "Bot de test", "testChatbot": "Chat<PERSON> de test", "aChatbot": "Un chatbot de test", "aChatbotDescription": "Description d'un chatbot de test", "myFlow": "Mon flux", "lastUpdatedToday": "Dernière mise à jour aujourd'hui"}, "whatsapp": {"onboarding": {"ngage": {"description": "Intégrez le WABA en utilisant le canal NGAGE WhatsApp et intégrez-le avec votre chatbot."}, "meta": {"description": "Intégrez le WABA en utilisant l'API Meta Cloud et liez votre chatbot via un partenaire BSP."}}}, "loading": {"hangTight": "Patientez ! Nous configurons votre espace de travail...", "almostThere": "Presque terminé..."}, "timePicker": {"hour": "<PERSON><PERSON>", "min": "Minute", "amPm": "AM/PM"}, "richTextEditor": {"bold": "Gras", "italic": "Italique", "underline": "<PERSON><PERSON><PERSON>", "strikeThrough": "<PERSON><PERSON>", "highlight": "<PERSON><PERSON><PERSON><PERSON>", "superscriptSubscript": "Exposant/Indice", "emoji": "<PERSON><PERSON><PERSON>"}, "notification": {"notificationChannel": "Sélectionnez le canal de notification", "configureMessage": "Configurer le message", "configureSMS": "Configurer le SMS", "selectSenderID": "Sélectionnez l'ID de l'expéditeur", "recipientMSISDN": "Entrez le MSISDN du destinataire", "configureEmail": "Configurer l'e-mail", "selectEmail": "Sélectionnez l'adresse e-mail", "recipientEmail": "Entrez l'adresse e-mail du destinataire", "enterSubjectOfEmail": "Entrez l'objet de l'e-mail"}}