import type { NodePort } from '../types/editor';

// Import all node icons
import start from '../assets/node-icons/play.svg';
import stop from '../assets/node-icons/square.svg';
import whatsapp from '../assets/editor/nodes/whatsapp.svg';

import agentTransfer from '../assets/node-icons/arrow-left-right.svg';
import choiceNode from '../assets/node-icons/sliders-horizontal.svg';
import flowConnector from '../assets/node-icons/workflow.svg';
import http from '../assets/node-icons/globe.svg';
import language from '../assets/node-icons/languages.svg';
import payment from '../assets/node-icons/banknote.svg';
import script from '../assets/node-icons/file-code-2.svg';
import waitDelay from '../assets/node-icons/hourglass.svg';

import feedback from '../assets/node-icons/message-circle-reply.svg';
import interactiveMessage from '../assets/node-icons/messages-square.svg';
import message from '../assets/node-icons/message-circle.svg';
import form from '../assets/node-icons/layout-list.svg';
import notification from '../assets/node-icons/bell-ring.svg';
import {
  jointJsEditorTheme,
  StencilNodes,
  StencilNodesType,
  StencilTabs,
  StencilTabsType,
} from './constants';

type ModuleType = string;

export const nodePanelTabs = [
  { name: StencilTabs.ENGAGE, key: StencilTabs.ENGAGE },
  { name: StencilTabs.UTILITIES, key: StencilTabs.UTILITIES },
  { name: StencilTabs.MARKETPLACE, key: StencilTabs.MARKETPLACE },
];

export const nodesData: Record<
  StencilNodesType,
  {
    category: StencilTabsType;
    icon: string; // SVG file path as string
    label: string;
    tags: string[];
    description: string;
    disabled?: boolean;
    // iconSize?: number; // Optional property for icon size
  }
> = {
  [StencilNodes.APP_START]: {
    category: StencilTabs.UTILITIES,
    icon: start,
    label: 'Trigger',
    tags: ['Trigger'],
    description: 'Journey initiation point.',
  },
  [StencilNodes.APP_END]: {
    category: StencilTabs.UTILITIES,
    icon: stop,
    label: 'End',
    tags: ['End'],
    description: 'Journey endpoint.',
  },
  [StencilNodes.WHATSAPP]: {
    category: StencilTabs.MARKETPLACE,
    icon: whatsapp,
    label: 'Whatsapp',
    tags: ['Messaging', 'Communication'],
    description: 'Send personalized WhatsApp messages to your customers.',
    disabled: true,
  },
  [StencilNodes.AGENT_TRANSFER]: {
    category: StencilTabs.UTILITIES,
    icon: agentTransfer,
    label: 'Agent Transfer',
    tags: ['Messaging', 'Communication'],
    description: 'Transfer the conversation to an agent.',
  },
  [StencilNodes.MESSAGE]: {
    category: StencilTabs.ENGAGE,
    icon: message,
    label: 'Message',
    tags: ['Message'],
    description: 'Send a message to the customer.',
  },
  [StencilNodes.FORM]: {
    category: StencilTabs.ENGAGE,
    icon: form,
    label: 'Form',
    tags: ['Form'],
    description: 'Send a Form to the customer.',
  },
  [StencilNodes.HTTP]: {
    category: StencilTabs.UTILITIES,
    icon: http,
    label: 'HTTP',
    tags: ['HTTP'],
    description: 'Send HTTP requests to external APIs.',
  },
  [StencilNodes.CHOICE]: {
    category: StencilTabs.UTILITIES,
    icon: choiceNode,
    label: 'Choice',
    tags: ['Choice'],
    description: 'Create a choice node.',
  },
  [StencilNodes.FLOW_CONNECTOR]: {
    category: StencilTabs.UTILITIES,
    icon: flowConnector,
    label: 'Flow Connector',
    tags: ['Flow Connector'],
    description: 'Connect different flows.',
  },
  [StencilNodes.LANGUAGE]: {
    category: StencilTabs.UTILITIES,
    icon: language,
    label: 'Language',
    tags: ['Language'],
    description: 'Change the language of the conversation.',
    disabled: true,
  },
  [StencilNodes.PAYMENT]: {
    category: StencilTabs.UTILITIES,
    icon: payment,
    label: 'Payment',
    tags: ['Payment'],
    description: 'Collect payment from the customer.',
    disabled: true,
  },
  [StencilNodes.SCRIPT]: {
    category: StencilTabs.UTILITIES,
    icon: script,
    label: 'Script',
    tags: ['Logic', 'Scripting'],
    description: 'Execute custom scripts.',
    disabled: true,
  },
  [StencilNodes.WAIT_DELAY]: {
    category: StencilTabs.UTILITIES,
    icon: waitDelay,
    label: 'Wait',
    tags: ['Wait'],
    description: 'Wait for a specified amount of time.',
    disabled: true,
  },
  [StencilNodes.INTERACTIVE_MESSAGE]: {
    category: StencilTabs.ENGAGE,
    icon: interactiveMessage,
    label: 'Interactive Message',
    tags: ['Interactive Message'],
    description: 'Send an interactive message to the customer.',
    disabled: true,
  },
  [StencilNodes.FEEDBACK]: {
    category: StencilTabs.ENGAGE,
    icon: feedback,
    label: 'Feedback',
    tags: ['Feedback'],
    description: 'Collect feedback from the customer.',
    // disabled: true,
  },
  [StencilNodes.NOTIFICATION]: {
    category: StencilTabs.ENGAGE,
    icon: notification,
    label: 'Notification',
    tags: ['Notification'],
    description: 'Send a notification to the customer.',
  },
};

export const nodeKeys = Object.keys(nodesData) as StencilNodesType[];
export const tempEnableNodes = Object.entries(nodesData)
  .filter(node => !node[1].disabled)
  .reduce(
    (acc, key) => ({
      ...acc,
      [key[0]]: true,
    }),
    {}
  );

export const getModuleIcon = (moduleType: ModuleType): string => {
  return nodesData[moduleType as StencilNodesType]?.icon || start;
};

export const getType = (moduleType: ModuleType) => {
  return nodesData[moduleType as StencilNodesType].category || StencilTabs.UTILITIES;
};

export const getModuleText = (moduleType: ModuleType) => {
  return nodesData[moduleType as StencilNodesType].label || 'No Data At All';
};

export const getModuleDescription = (moduleType: ModuleType) => {
  return nodesData[moduleType as StencilNodesType].description || 'No Data desc';
};

export const getModuleTags = (moduleType: ModuleType) => {
  return nodesData[moduleType as StencilNodesType].tags || ['No Data tag'];
};

export const portsIn: NodePort = {
  id: '',
  group: 'in',
  attrs: {
    portBody: {
      magnet: true,
      r: 4,
      fill: 'transparent',
      stroke: 'transparent',
    },
  },
};

export const portsOut: NodePort = {
  id: '',
  group: 'out',
  attrs: {
    portBody: {
      magnet: true,
      r: 4,
      fill: jointJsEditorTheme.primaryColor,
      stroke: jointJsEditorTheme.primaryColor,
      x: -8,
      y: -8,
    },
  },
};

export const isHelperTextNeeded = (type: ModuleType): boolean => {
  return type !== 'appStart' && type !== 'appEnd';
};

export const getTriggers = (): Record<string, string> => {
  return {
    standard: 'Standard',
    whatsapp: 'WhatsApp',
    telegram: 'Telegram',
    webhook: 'Webhook',
  };
};

export const getUniqueIds = (trigger: string): string => {
  switch (trigger) {
    case 'whatsapp':
      return 'From';
    case 'telegram':
      return 'Phone Number';
    default:
      return 'Unique ID';
  }
};
