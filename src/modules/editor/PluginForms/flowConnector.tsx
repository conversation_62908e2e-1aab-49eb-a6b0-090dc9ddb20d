import { useMemo, useEffect } from 'react';
import { useFormContext } from 'react-hook-form';
import { useTranslation } from 'react-i18next';

import { FloatingField, FloatingType } from '@/components/ui/floating-label';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { FormControl, FormField, FormItem } from '@/components/ui/form';

import { useGetFlowsQuery } from '@/store/api/flowApi';
import { useBotIdParam } from '@/hooks/useRouterParam';
import { useAppSelector } from '@/hooks/useRedux';

export default function FlowConnector() {
  const { t } = useTranslation();
  const { control, watch, setValue } = useFormContext();
  const selectedFlowId = useAppSelector(state => state.flows.activeFlowId);
  const { botId } = useBotIdParam();

  const { data: flowsData } = useGetFlowsQuery({
    botId,
  });

  const currentTargetFlowId = watch('process.flowConfig.targetFlowId');

  const flowOptions = useMemo(() => {
    if (!flowsData) return [];

    return flowsData?.data?.items
      .filter(f => f.id !== selectedFlowId?.id)
      .map(f => ({
        label: f.name,
        value: f.id,
      }));
  }, [flowsData, selectedFlowId]);

  // Auto-set journeyId when targetFlowId changes
  useEffect(() => {
    if (currentTargetFlowId && flowsData) {
      const selectedFlow = flowsData.data?.items.find(f => f.id === currentTargetFlowId);
      if (selectedFlow?.appId) {
        setValue('process.flowConfig.journeyId', selectedFlow.appId);
      }
    }
  }, [currentTargetFlowId, flowsData, setValue]);

  return (
    <div className="flex flex-col gap-4 px-4 py-4">
      {/* Target Flow ID */}
      <FormField
        control={control}
        name="process.flowConfig.targetFlowId"
        render={({ field }) => (
          <FormItem>
            <FormControl>
              <FloatingField
                label={t('flows.targetFlow')}
                as={FloatingType.SELECT}
                value={field.value || ''}
                onChange={field.onChange}
                options={flowOptions}
              />
            </FormControl>
          </FormItem>
        )}
      />

      {/* Pass Existing Context Switch */}
      <FormField
        control={control}
        name="process.flowConfig.passExistingContext"
        render={({ field }) => (
          <FormItem>
            <div className="flex items-center justify-between">
              <Label className="text-xs">{t('flows.existingFlow')}</Label>
              <FormControl>
                <Switch checked={field.value ?? true} onCheckedChange={field.onChange} />
              </FormControl>
            </div>
          </FormItem>
        )}
      />

      {/* Resume From This Context Switch */}
      {/* <FormField
        control={control}
        name="process.flowConfig.resumeFromThisContext"
        render={({ field }) => (
          <FormItem>
            <div className="flex items-center justify-between">
              <Label className="text-sm">{t('Resume From This Context')}</Label>
              <FormControl>
                <Switch checked={field.value ?? false} onCheckedChange={field.onChange} />
              </FormControl>
            </div>
          </FormItem>
        )}
      /> */}
    </div>
  );
}
