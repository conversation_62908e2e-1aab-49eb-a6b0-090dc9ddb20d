import { dia } from 'rappid';
import React, { useCallback, useEffect, useRef, useState } from 'react';

import Stencil from './widget/Stencil/index';

import { useGetApplicationDetailsQuery } from '@/store/api/studioApi';
import { useEditor } from './hooks/useEditor';
import { joinToLeapJSON } from './utils/jointJsToLeap';
import { leapToJointJSON } from './utils/leapToJointJs';
import { useUndoRedo } from '@/modules/editor/hooks/useUndoRedo';
import useNodeHandler from '@/modules/editor/hooks/useEditorNodeHandler';
import NodeConfigSheet from './NodeConfigSheet';
import { useFormHandler } from './hooks/useFormHandler';
import { SettingDetails } from './types';
import { Application } from '@/types';

interface IProps {
  id: string;
  jsonDetails?: Application;
  readOnly?: boolean;
}

const NeuraTalkEditor = ({ id, jsonDetails, readOnly = false }: IProps) => {
  const [settingDetails, setSettingDetails] = useState<SettingDetails>({});
  const [isEditorLoading, setEditorLoading] = useState(false);
  const [error, setError] = useState(null);
  const canvasWithPallette = useRef<HTMLDivElement>(null);
  const isEdit = !readOnly;

  const {
    data: applicationData,
    error: fetchError,
    isLoading,
  } = useGetApplicationDetailsQuery({ appId: id }, { skip: !id || !!jsonDetails });

  const changeJointJsonToLeapOnDrop = useCallback(
    (graph: dia.Graph, updateData = null) => {
      //TODO: add Proper Type
      if (graph) {
        const data = joinToLeapJSON(
          JSON.parse(JSON.stringify(graph.toJSON())),
          updateData || settingDetails,
          graph
        ).modules;

        const { nodes, jsonData } = leapToJointJSON(data);

        graph.fromJSON(nodes);

        setSettingDetails(prev => {
          return {
            ...prev,
            ...jsonData,
          };
        });
      }
    },
    [settingDetails, setSettingDetails]
  );

  const {
    canvas,
    graphInstance,
    paperInstance,
    initializeEditor,
    loadApplicationData,
    setAppDetails,
    autoUpdateHandler,
    modalTypeDetails,
    setModalTypeDetails,
    scrollInstance,
    currentElementView,
    setCurrentElementView,
  } = useEditor({
    canvasWithPallette,
    isEdit,
    changeJointJsonToLeapOnDrop,
    id,
    settingDetails,
    setSettingDetails,
    setEditorLoading,
  });

  const { handleFormClose } = useFormHandler({
    setModalTypeDetails,
    settingDetails,
    scrollInstance,
    graphInstance,
    currentElementView,
    setCurrentElementView,
  });

  const { updateCurrentDetails, removeLastUndoState, updateInitialState, currentIndex, lastIndex } =
    useUndoRedo();

  const { handleNodeAdd, isMaxNodeReached, handleSettingsUpdate } = useNodeHandler({
    graphInstance,
    changeJointJsonToLeapOnDrop,
    autoUpdateHandler,
    settingDetails,
    setError,
    setSettingDetails,
    modalTypeDetails,
  });

  const canUndo = currentIndex > 0;
  const canRedo = currentIndex < lastIndex;

  useEffect(() => {
    const cleanup = initializeEditor();
    return () => {
      initialUpdateRef.current = '';
      cleanup?.();
    };
  }, []);

  const initialUpdateRef = useRef('');

  useEffect(() => {
    if (!graphInstance || !paperInstance) return;
    if (jsonDetails) {
      setEditorLoading(true);
      //TODO: need to use proper type here
      updateInitialState({ payload: jsonDetails as any, action: 'initial' });
      setAppDetails(jsonDetails as any);
      loadApplicationData(jsonDetails as any);
    } else if (applicationData && id != initialUpdateRef.current) {
      setEditorLoading(true);
      initialUpdateRef.current = id;
      //TODO: fix this
      updateInitialState({
        payload: applicationData as any,
        action: 'initial',
      });
      setAppDetails(applicationData as any);
      loadApplicationData(applicationData as any);
    }
  }, [applicationData, graphInstance, paperInstance, jsonDetails]);

  const isStencilEnabled = isEdit && paperInstance && graphInstance;

  return (
    <div className="studio-root">
      <div
        className="canvas-wrapper min-h-screen full-screen"
        ref={canvasWithPallette}
        style={{ position: 'relative', height: 'calc(100% - 80px)' }}
      >
        <div className="canvas" ref={canvas}>
          <div id="react-portal-modal-container" />
        </div>
        {isStencilEnabled && (
          <div>
            <Stencil
              paper={paperInstance}
              graph={graphInstance}
              handleNodeAdd={handleNodeAdd}
              canvasWithPallette={canvasWithPallette as any}
              isMaxNodeReached={isMaxNodeReached}
            />
          </div>
        )}

        {isEdit && (
          <NodeConfigSheet
            handleClose={handleFormClose}
            modalTypeDetails={modalTypeDetails}
            // startUrl={startUrl}
            settingDetails={settingDetails}
            handleSave={handleSettingsUpdate} //TODO: need to add proper type
            isEdit={isEdit}
            // isPublishedEnabled={isPublishedEnabled}
          />
        )}

        {/* <ErrorModal
          details={error}
          open={!!error}
          onClose={() => {
            updateError(null);
          }}
        />
        <ConfirmBox
          open={publishConfirmStatus}
          onSuccess={updateAppJson}
          onClose={() => {
            updateConfirmStatus(false);
          }}
        /> */}
        {/* {isNodeModalOpen && (
          <AutoSuggestion
            onClose={toggleNodeModal}
            graph={graphInstance}
            autoSuggestionPosition={autoSuggestionPosition}
            changeJointJsonToLeapOnDrop={changeJointJsonToLeapOnDrop}
            isMaxNodeReached={isMaxNodeReached}
          />
        )} */}
        {/* <SuccessModal
          open={publishSuccessModal}
          onClose={() => {
            updatePublishSuccessModal(false);
          }}
          title={appDetails?.name}
        /> */}
      </div>
    </div>
  );
};

export default NeuraTalkEditor;
