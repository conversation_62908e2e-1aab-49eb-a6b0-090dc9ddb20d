import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import EmptyState from '@/components/EmptyState';
import UtteranceCard from './UtteranceCard';
import { IntentItem, IntentUtteranceTranslation, ModalState } from '@/types';
import {
  useGetIntentUtteranceTranslationsQuery,
  useDeleteIntentUtteranceTranslationMutation,
} from '@/store/api';
import AddModal from '../components/AddModal';
import AddUtteranceForm from './AddUtteranceForm';
import { useToast } from '@/hooks/use-toast';
import SuccessToastMessage from '@/components/SuccessToastMessage';
import DeleteConfirmationModal from '@/components/DeleteConfirmationModal';
import {
  PaginationProvider,
  usePagination,
  PaginationRenderItems,
  PaginationLoader,
  PaginationEmptyState,
  PaginationError,
} from '@/components/Pagination';

interface IProps {
  selectedIntent: IntentItem;
  language: string;
  AddUtteranceModal: React.ReactNode;
}

const UtteranceList: React.FC<IProps> = ({ selectedIntent, language, AddUtteranceModal }) => {
  const { t } = useTranslation();
  const { toast } = useToast();
  const [modalState, setModalState] = useState<ModalState<IntentUtteranceTranslation> | null>(null);

  const pagination = usePagination({
    queryHook: query =>
      useGetIntentUtteranceTranslationsQuery(
        {
          intentId: selectedIntent.id,
          langId: language,
          query,
        },
        { skip: !language }
      ),
  });

  const [deleteIntentUtteranceTranslation] = useDeleteIntentUtteranceTranslationMutation();

  const handleEdit = (utterance: IntentUtteranceTranslation) => {
    setModalState({ type: 'edit', item: utterance });
  };

  const handleDelete = (utterance: IntentUtteranceTranslation) => {
    setModalState({ type: 'delete', item: utterance });
  };

  const confirmDelete = async () => {
    if (!modalState || modalState.type !== 'delete') return;
    try {
      await deleteIntentUtteranceTranslation({ id: modalState.item.id }).unwrap();
      toast({
        title: <SuccessToastMessage message={t('intents.utterances.utteranceDeleted')} />,
      });
    } catch (error: any) {
      console.error('Failed to delete utterance:', error);
      toast({
        title: t('common.error'),
        description: error,
        variant: 'destructive',
      });
    } finally {
      handleCloseModal();
    }
  };

  const handleCloseModal = () => {
    setModalState(null);
  };

  return (
    <PaginationProvider value={pagination}>
      <div className="space-y-4 overflow-auto h-full">
        <PaginationRenderItems<IntentUtteranceTranslation>
          renderItems={items =>
            items.map(utterance => (
              <UtteranceCard
                key={utterance.id}
                utteranceTranslation={utterance}
                onEdit={handleEdit}
                onDelete={handleDelete}
              />
            ))
          }
        />

        <PaginationLoader />

        <PaginationEmptyState>
          <EmptyState
            title={t('intents.utterances.startAdding')}
            description={t('common.nothingToShow')}
          >
            <div className="mt-5">{AddUtteranceModal}</div>
          </EmptyState>
        </PaginationEmptyState>

        <PaginationError />
      </div>

      {modalState?.type === 'edit' && (
        <AddModal
          title={t('intents.utterances.editTitle')}
          open={true}
          onOpenChange={handleCloseModal}
          className="sm:max-w-[850px]"
        >
          <AddUtteranceForm
            intentId={selectedIntent.id}
            onClose={handleCloseModal}
            utteranceTranslationNode={modalState.item}
            selectedLangId={language}
          />
        </AddModal>
      )}

      <DeleteConfirmationModal
        isOpen={modalState?.type === 'delete'}
        onClose={handleCloseModal}
        onConfirm={confirmDelete}
        title={t('intents.utterances.confirmDeleteTitle')}
        description={t('intents.utterances.deleteConfirmationMessage')}
      />
    </PaginationProvider>
  );
};

export default UtteranceList;
