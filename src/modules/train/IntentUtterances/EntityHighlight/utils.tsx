
interface ParsedEntity {
  start: number;
  end: number;
  text: string;
  name: string;
  id: string;
  type: string;
}

interface HoveredEntity {
  id: string;
  name: string;
  type: string;
  x: number;
  y: number;
  displayStart: number;
}

interface SelectionState {
  text: string;
  start: number;
  end: number;
}

interface HistoryState {
  undo: string[];
  redo: string[];
}

// Utility Functions
const parseEntities = (text: string): ParsedEntity[] => {
  const entityRegex = /\[([^\]]+)\]\(([^_]+)_([^_]+)_([^)]+)\)/g;
  const entities: ParsedEntity[] = [];
  let match;
  while ((match = entityRegex.exec(text)) !== null) {
    entities.push({
      start: match.index,
      end: match.index + match[0].length,
      text: match[1],
      name: match[2],
      id: match[3],
      type: match[4],
    });
  }
  return entities.sort((a, b) => a.start - b.start);
};

const getDisplayText = (text: string): string =>
  text.replace(/\[([^\]]+)\]\(([^_]+)_([^_]+)_([^)]+)\)/g, '$1');

const colors = {
  TEXT: 'bg-primary-100 text-primary-800 border-primary-200',
  EMAIL: 'bg-success-100 text-success-800 border-success-200',
  DATE: 'bg-purple-100 text-purple-800 border-purple-200',
  NUMBER: 'bg-orange-100 text-orange-800 border-orange-200',
  REGEX: 'bg-pink-100 text-pink-800 border-pink-200',
};

const getEntityColor = (type: string): string => {
  return (
    colors[type as keyof typeof colors] || 'bg-tertiary-100 text-tertiary-800 border-tertiary-200'
  );
};

// Reconcile typed text with existing entities (keeps surviving entities intact)
const reconcileTextWithEntities = (newText: string, oldValue: string): string => {
  const oldEntities = parseEntities(oldValue);
  if (oldEntities.length === 0) return newText;

  const displayEntities = oldEntities.map(entity => ({
    ...entity,
    displayStart: getDisplayText(oldValue.substring(0, entity.start)).length,
    displayEnd: getDisplayText(oldValue.substring(0, entity.start)).length + entity.text.length,
  }));

  let newValue = '';
  let currentPos = 0;
  for (const entity of displayEntities) {
    if (
      entity.displayStart <= newText.length &&
      newText.substring(entity.displayStart, entity.displayEnd) === entity.text
    ) {
      if (entity.displayStart > currentPos) {
        newValue += newText.substring(currentPos, entity.displayStart);
      }
      newValue += `[${entity.text}](${entity.name}_${entity.id}_${entity.type})`;
      currentPos = entity.displayEnd;
    }
  }
  if (currentPos < newText.length) {
    newValue += newText.substring(currentPos);
  }

  return newValue;
};

// Expand partial selection to whole word on the display text
const expandToWord = (text: string, start: number, end: number) => {
  while (start > 0 && /\S/.test(text[start - 1])) start--;
  while (end < text.length && /\S/.test(text[end])) end++;
  return { start, end, word: text.slice(start, end) };
};



