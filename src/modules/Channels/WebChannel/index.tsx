import React, { useState } from 'react';
import ChatbotWidget from 'chatbot-sdk/ChatbotWidget';
import { useTranslation } from 'react-i18next';
import WebChannelSettings from './components/WebChannelSettings';
import { WebChannelConfig } from './types';
import { useBotIdParam } from '@/hooks/useRouterParam';
import { FONT_FAMILY_OPTIONS, FONT_SIZE_OPTIONS } from './config';

const WebChannelView: React.FC = () => {
  const { t } = useTranslation();
  const { botId } = useBotIdParam();

  const [config, setConfig] = useState<WebChannelConfig>({
    botName: 'Bot',
    botDescription: t('channels.defaultBotDescription'),
    primaryColor: '#496FDB',
    secondaryColor: '#64748b',
    tertiaryColor: '#FAFAFA',
    fontFamily: FONT_FAMILY_OPTIONS[0].value,
    fontSize: FONT_SIZE_OPTIONS[1].value,
    botAvatarUrl: 'https://ik.imagekit.io/kggne4pv7or/bot_AEwXH-8cV.svg',
    botId,
  });

  const handleConfigChange = (field: keyof WebChannelConfig, value: string) => {
    setConfig(prevConfig => ({
      ...prevConfig,
      [field]: value,
    }));
  };

  return (
    <div className="flex flex-col lg:flex-row flex-1">
      <WebChannelSettings config={config} onConfigChange={handleConfigChange} />

      {/* Right Panel: Preview */}
      <div className="flex flex-[0.8] flex-col items-center pt-3 bg-tertiary-50">
        <div className="flex justify-end font-medium mb-4">{t('common.preview')}</div>
        <div className="flex-1 flex flex-col">
          <ChatbotWidget config={config} onClose={() => {}} onMinimize={() => {}} />
        </div>
      </div>
    </div>
  );
};

export default WebChannelView;