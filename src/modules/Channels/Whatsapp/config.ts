import { OnboardingOption, WABANumber } from './types';

export enum WhatsappViewTab {
  ONBOARDING = 'onboarding',
  WABA_SELECTION = 'waba-selection',
  CONFIGURATION = 'configuration',
}

export enum OnboardingOptionId {
  Ngage = 'ngage',
  Meta = 'meta',
}

export const onboardingOptions: OnboardingOption[] = [
  {
    id: OnboardingOptionId.Ngage,
    title: 'NGAGE',
    descriptionKey: 'whatsapp.onboarding.ngage.description',
    icon: '/api/placeholder/32/32',
    buttonTextKey: 'common.getStarted',
  },
  {
    id: OnboardingOptionId.Meta,
    title: 'Meta Cloud API',
    descriptionKey: 'whatsapp.onboarding.meta.description',
    icon: '/api/placeholder/32/32',
    buttonTextKey: 'common.getStarted',
    disabled: true,
  },
];
